<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Error | CRM System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .container {
            text-align: center;
            background: white;
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            margin: 2rem;
        }
        
        .error-code {
            font-size: 6rem;
            font-weight: bold;
            color: #ff6b6b;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .error-message {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #555;
        }
        
        .error-description {
            font-size: 1rem;
            color: #777;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 30px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 500;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin: 0.5rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(255, 107, 107, 0.3);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        }
        
        .btn-secondary:hover {
            box-shadow: 0 10px 20px rgba(116, 185, 255, 0.3);
        }
        
        .icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        .status-info {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            font-size: 0.9rem;
            color: #666;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 2rem;
                margin: 1rem;
            }
            
            .error-code {
                font-size: 4rem;
            }
            
            .error-message {
                font-size: 1.2rem;
            }
            
            .btn {
                display: block;
                margin: 0.5rem 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">⚠️</div>
        <div class="error-code">5xx</div>
        <div class="error-message">Server Error</div>
        <div class="error-description">
            We're experiencing technical difficulties. Our team has been notified and is working to resolve the issue.
        </div>
        <div class="status-info">
            <strong>What you can do:</strong><br>
            • Try refreshing the page in a few minutes<br>
            • Check our status page for updates<br>
            • Contact support if the problem persists
        </div>
        <a href="/" class="btn">Return to Homepage</a>
        <a href="javascript:location.reload()" class="btn btn-secondary">Refresh Page</a>
    </div>
    
    <script>
        // Auto-refresh after 30 seconds
        setTimeout(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
