#!/bin/bash

# Generate SSL Certificate for CRM System
# Creates self-signed certificate for development/testing

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
SSL_DIR="./docker/nginx/ssl"
CERT_FILE="$SSL_DIR/server.crt"
KEY_FILE="$SSL_DIR/server.key"
DOMAIN="localhost"
COUNTRY="US"
STATE="State"
CITY="City"
ORG="CRM System"
DAYS=365

print_status "Generating SSL certificate for CRM System..."

# Create SSL directory if it doesn't exist
if [ ! -d "$SSL_DIR" ]; then
    mkdir -p "$SSL_DIR"
    print_status "Created SSL directory: $SSL_DIR"
fi

# Check if certificate already exists
if [ -f "$CERT_FILE" ] && [ -f "$KEY_FILE" ]; then
    print_warning "SSL certificate already exists!"
    read -p "Do you want to overwrite it? (y/N): " -r
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Keeping existing certificate"
        exit 0
    fi
fi

# Generate private key
print_status "Generating private key..."
openssl genrsa -out "$KEY_FILE" 2048

# Generate certificate signing request
print_status "Generating certificate signing request..."
openssl req -new -key "$KEY_FILE" -out "$SSL_DIR/server.csr" -subj "/C=$COUNTRY/ST=$STATE/L=$CITY/O=$ORG/CN=$DOMAIN"

# Generate self-signed certificate
print_status "Generating self-signed certificate..."
openssl x509 -req -days $DAYS -in "$SSL_DIR/server.csr" -signkey "$KEY_FILE" -out "$CERT_FILE"

# Set proper permissions
chmod 600 "$KEY_FILE"
chmod 644 "$CERT_FILE"

# Clean up CSR file
rm "$SSL_DIR/server.csr"

print_success "SSL certificate generated successfully!"
print_status "Certificate: $CERT_FILE"
print_status "Private Key: $KEY_FILE"
print_status "Valid for: $DAYS days"

# Verify certificate
print_status "Verifying certificate..."
if openssl x509 -in "$CERT_FILE" -text -noout > /dev/null 2>&1; then
    print_success "Certificate verification passed"
    
    # Show certificate details
    echo ""
    echo "Certificate Details:"
    openssl x509 -in "$CERT_FILE" -subject -dates -noout
else
    print_error "Certificate verification failed"
    exit 1
fi

print_success "SSL certificate setup complete!"
print_warning "Note: This is a self-signed certificate for development use only"
print_status "For production, use a certificate from a trusted CA (Let's Encrypt, etc.)"
