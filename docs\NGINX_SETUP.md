# Nginx Configuration for CRM System

## 📋 Overview

This document describes the Nginx reverse proxy setup for the CRM system, designed for Docker deployment with production-ready features.

## 🏗️ Architecture

```
Internet → Nginx (Port 80/443) → Backend (Port 5000) + Frontend (Port 5173)
```

### Components
- **Nginx**: Reverse proxy, SSL termination, static file serving, caching
- **Backend**: Node.js API server (internal port 5000)
- **Frontend**: React application (internal port 5173)

## 📁 File Structure

```
docker/nginx/
├── Dockerfile              # Nginx container build
├── nginx.conf              # Production configuration
├── nginx.dev.conf          # Development configuration
├── html/
│   ├── 404.html            # Custom 404 page
│   └── 50x.html            # Custom error page
└── ssl/
    ├── server.crt          # SSL certificate (place here)
    ├── server.key          # SSL private key (place here)
    └── .gitkeep            # Directory placeholder
```

## 🚀 Quick Start

### 1. Production Deployment
```bash
# Build and start with Docker Compose
docker-compose -f docker-compose.production.yml up -d

# Check Nginx status
docker logs crm_nginx_prod

# Test configuration
curl -I https://localhost/health
```

### 2. Development Setup
```bash
# Use development configuration
docker run -d \
  --name nginx-dev \
  -p 80:80 \
  -v $(pwd)/docker/nginx/nginx.dev.conf:/etc/nginx/nginx.conf \
  nginx:alpine
```

## 🔧 Configuration Features

### Production (`nginx.conf`)

#### Security Features
- **SSL/TLS**: Modern TLS 1.2/1.3 configuration
- **HSTS**: HTTP Strict Transport Security
- **Security Headers**: X-Frame-Options, X-XSS-Protection, CSP
- **Rate Limiting**: API, auth, and general endpoints
- **File Upload Security**: Blocks dangerous file types

#### Performance Features
- **Gzip Compression**: Text, CSS, JS, JSON compression
- **Caching**: Static assets, API responses, uploaded files
- **Keep-Alive**: Connection reuse
- **Buffer Optimization**: Proxy buffering settings

#### Routing
- **Frontend**: SPA routing with fallback
- **API**: `/api/*` routes to backend
- **Uploads**: `/uploads/*` serves static files
- **WebSocket**: `/socket.io/*` for real-time features

### Development (`nginx.dev.conf`)
- Simplified configuration
- No SSL (HTTP only)
- No caching (for development)
- WebSocket support for Vite HMR

## 🔒 SSL Configuration

### Option 1: Bring Your Own Certificate
```bash
# Place your certificates in docker/nginx/ssl/
cp your-certificate.crt docker/nginx/ssl/server.crt
cp your-private-key.key docker/nginx/ssl/server.key
```

### Option 2: Self-Signed Certificate (Development)
The Dockerfile automatically generates a self-signed certificate if none is provided.

### Option 3: Let's Encrypt (Production)
```bash
# Install certbot
sudo apt install certbot

# Generate certificate
sudo certbot certonly --standalone -d yourdomain.com

# Copy to nginx directory
sudo cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem docker/nginx/ssl/server.crt
sudo cp /etc/letsencrypt/live/yourdomain.com/privkey.pem docker/nginx/ssl/server.key
```

## 📊 Rate Limiting

| Zone | Limit | Burst | Endpoints |
|------|-------|-------|-----------|
| `general` | 30 req/s | 50 | All routes |
| `api` | 10 req/s | 20 | `/api/*` |
| `auth` | 5 req/min | 5 | `/api/auth/*` |

## 🗂️ Caching Strategy

### Static Assets
- **Duration**: 1 year
- **Files**: JS, CSS, images, fonts
- **Headers**: `Cache-Control: public, immutable`

### API Responses
- **Duration**: 5 minutes
- **Endpoints**: `/api/health`, `/api/products`, `/api/categories`
- **Strategy**: Stale-while-revalidate

### Uploaded Files
- **Duration**: 30 days
- **Files**: Product images
- **Headers**: `Cache-Control: public, no-transform`

## 🔍 Monitoring & Health Checks

### Health Check Endpoint
```bash
# Check Nginx health
curl http://localhost/health

# Expected response: "healthy"
```

### Docker Health Check
```yaml
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost/health"]
  interval: 30s
  timeout: 10s
  retries: 3
```

### Log Locations
- **Access Log**: `/var/log/nginx/access.log`
- **Error Log**: `/var/log/nginx/error.log`
- **Docker Logs**: `docker logs crm_nginx_prod`

## 🛠️ Customization

### Environment Variables
```bash
# In docker-compose.yml
environment:
  - NGINX_ENVSUBST_TEMPLATE_SUFFIX=.template
  - NGINX_ENVSUBST_OUTPUT_DIR=/etc/nginx
```

### Custom Error Pages
Edit files in `docker/nginx/html/`:
- `404.html` - Page not found
- `50x.html` - Server errors

### Additional Locations
Add to `nginx.conf`:
```nginx
location /custom/ {
    proxy_pass http://custom-service:8080;
    # Additional configuration
}
```

## 🚨 Troubleshooting

### Common Issues

#### 1. SSL Certificate Errors
```bash
# Check certificate validity
openssl x509 -in docker/nginx/ssl/server.crt -text -noout

# Verify private key matches
openssl rsa -in docker/nginx/ssl/server.key -check
```

#### 2. Backend Connection Issues
```bash
# Test backend connectivity
docker exec crm_nginx_prod curl -I http://backend:5000/api/health

# Check network
docker network ls
docker network inspect crm_network
```

#### 3. Rate Limiting Issues
```bash
# Check rate limit zones
docker exec crm_nginx_prod nginx -T | grep limit_req_zone

# Reset rate limits (restart nginx)
docker restart crm_nginx_prod
```

#### 4. Caching Issues
```bash
# Clear nginx cache
docker exec crm_nginx_prod rm -rf /var/cache/nginx/*

# Disable caching temporarily
# Add to location block: proxy_cache off;
```

### Debug Mode
```bash
# Enable debug logging
docker exec crm_nginx_prod sed -i 's/error_log.*notice/error_log \/var\/log\/nginx\/error.log debug/' /etc/nginx/nginx.conf
docker restart crm_nginx_prod
```

## 📈 Performance Tuning

### Worker Processes
```nginx
# Set to number of CPU cores
worker_processes auto;

# Or specific number
worker_processes 4;
```

### Connection Limits
```nginx
events {
    worker_connections 2048;  # Increase for high traffic
    use epoll;                # Linux optimization
    multi_accept on;          # Accept multiple connections
}
```

### Buffer Sizes
```nginx
# Increase for large requests
client_max_body_size 50M;
proxy_buffer_size 8k;
proxy_buffers 16 8k;
```

## 🔄 Updates & Maintenance

### Configuration Updates
```bash
# Test configuration
docker exec crm_nginx_prod nginx -t

# Reload configuration (no downtime)
docker exec crm_nginx_prod nginx -s reload

# Full restart
docker restart crm_nginx_prod
```

### Certificate Renewal
```bash
# Renew Let's Encrypt certificate
sudo certbot renew

# Copy new certificate
sudo cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem docker/nginx/ssl/server.crt
sudo cp /etc/letsencrypt/live/yourdomain.com/privkey.pem docker/nginx/ssl/server.key

# Reload nginx
docker exec crm_nginx_prod nginx -s reload
```

## 📋 Security Checklist

- [ ] SSL certificate installed and valid
- [ ] HSTS header enabled
- [ ] Security headers configured
- [ ] Rate limiting enabled
- [ ] File upload restrictions in place
- [ ] Access logs enabled
- [ ] Error pages don't expose sensitive info
- [ ] Unnecessary HTTP methods disabled
- [ ] Server tokens hidden

## 🌐 Production Deployment

### Domain Configuration
1. Update `server_name` in `nginx.conf`
2. Configure DNS A record
3. Install SSL certificate
4. Test HTTPS redirect

### Load Balancing (Multiple Backends)
```nginx
upstream backend {
    server backend1:5000;
    server backend2:5000;
    server backend3:5000;
    keepalive 32;
}
```

---

**Last Updated**: July 2025  
**Version**: 1.0.0
